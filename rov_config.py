#!/usr/bin/env python3
"""
ROV仿真配置文件
在这里修改所有重要参数，然后在main中导入使用
"""

# ========================================
# 🎛️ ROV仿真统一配置参数
# ========================================

class ROVConfig:
    """ROV仿真配置类 - 统一管理所有参数"""
    
    # 📁 文件路径配置
    USD_FILE_PATH = "/home/<USER>/self_underwater_isaacsim/BUOYANCY_TEST.usd"
    
    # 🧊 物体物理属性
    OBJECT_MASS = 0.4           # kg - 物体质量（密度 = 质量/体积）
    OBJECT_VOLUME = 1.0         # m³ - 物体体积
    OBJECT_HEIGHT = 1.0         # m - 物体高度
    
    # 🌊 水环境参数
    WATER_DENSITY = 1.0         # kg/m³ - 水密度（1.0=淡水，1.025=海水）
    GRAVITY = 9.8               # m/s² - 重力加速度
    
    # 🎮 控制器参数
    PID_KP = 10.0               # 比例增益
    PID_KI = 1.0                # 积分增益  
    PID_KD = 0.1                # 微分增益
    PID_SAT_MAX = 50.0          # 控制器输出上限
    PID_SAT_MIN = -50.0         # 控制器输出下限
    
    # 🌀 阻尼参数
    MAX_DAMPING = 2.0           # 最大阻尼系数
    
    # 🚀 力限制参数（安全保护）
    MAX_BUOYANCY_FORCE = 50.0   # N - 最大浮力
    MAX_THRUSTER_FORCE = 20.0   # N - 最大推进器力
    MAX_CONTROLLER_FORCE = 30.0 # N - 最大控制器力
    MAX_TOTAL_FORCE = 100.0     # N - 最大总力
    
    # 🔧 仿真参数
    ENABLE_DEBUG = True         # 是否显示调试信息
    DISABLE_ISAAC_GRAVITY = True # 是否禁用Isaac Sim自动重力
    
    @classmethod
    def get_config_dict(cls):
        """获取配置字典"""
        return {
            'usd_file_path': cls.USD_FILE_PATH,
            'object_mass': cls.OBJECT_MASS,
            'object_volume': cls.OBJECT_VOLUME,
            'object_height': cls.OBJECT_HEIGHT,
            'water_density': cls.WATER_DENSITY,
            'gravity': cls.GRAVITY,
            'pid_kp': cls.PID_KP,
            'pid_ki': cls.PID_KI,
            'pid_kd': cls.PID_KD,
            'pid_sat_max': cls.PID_SAT_MAX,
            'pid_sat_min': cls.PID_SAT_MIN,
            'max_damping': cls.MAX_DAMPING,
            'max_buoyancy_force': cls.MAX_BUOYANCY_FORCE,
            'max_thruster_force': cls.MAX_THRUSTER_FORCE,
            'max_controller_force': cls.MAX_CONTROLLER_FORCE,
            'max_total_force': cls.MAX_TOTAL_FORCE,
            'debug_mode': cls.ENABLE_DEBUG,
            'disable_isaac_gravity': cls.DISABLE_ISAAC_GRAVITY
        }
    
    @classmethod
    def print_config_summary(cls):
        """打印配置摘要"""
        object_density = cls.OBJECT_MASS / cls.OBJECT_VOLUME
        theoretical_buoyancy = cls.WATER_DENSITY * cls.OBJECT_VOLUME * cls.GRAVITY
        theoretical_weight = cls.OBJECT_MASS * cls.GRAVITY
        net_force = theoretical_buoyancy - theoretical_weight
        
        print(f"📊 ROV配置摘要:")
        print(f"   物体: 质量={cls.OBJECT_MASS}kg, 体积={cls.OBJECT_VOLUME}m³, 密度={object_density:.2f}kg/m³")
        print(f"   环境: 水密度={cls.WATER_DENSITY}kg/m³, 重力={cls.GRAVITY}m/s²")
        print(f"   控制: PID({cls.PID_KP}, {cls.PID_KI}, {cls.PID_KD}), 限制±{cls.PID_SAT_MAX}N")
        print(f"   力限制: 浮力≤{cls.MAX_BUOYANCY_FORCE}N, 推进器≤{cls.MAX_THRUSTER_FORCE}N, 总力≤{cls.MAX_TOTAL_FORCE}N")
        print(f"   理论: 浮力={theoretical_buoyancy:.2f}N↑, 重力={theoretical_weight:.2f}N↓, 净力={net_force:.2f}N")
        print(f"   预期: {'上浮⬆️' if object_density < cls.WATER_DENSITY else '下沉⬇️' if object_density > cls.WATER_DENSITY else '悬浮⚖️'}")
        print()


# ========================================
# 🎯 预设配置方案
# ========================================

class ROVPresets:
    """ROV预设配置方案"""
    
    @staticmethod
    def get_floating_cube():
        """上浮方块配置 - 密度小于水"""
        config = ROVConfig.get_config_dict()
        config.update({
            'object_mass': 0.4,      # 密度0.4 < 1.0，应该上浮
            'water_density': 1.0,
            'debug_mode': True
        })
        return config
    
    @staticmethod
    def get_sinking_cube():
        """下沉方块配置 - 密度大于水"""
        config = ROVConfig.get_config_dict()
        config.update({
            'object_mass': 1.5,      # 密度1.5 > 1.0，应该下沉
            'water_density': 1.0,
            'debug_mode': True
        })
        return config
    
    @staticmethod
    def get_neutral_cube():
        """中性浮力方块配置 - 密度等于水"""
        config = ROVConfig.get_config_dict()
        config.update({
            'object_mass': 1.0,      # 密度1.0 = 1.0，应该悬浮
            'water_density': 1.0,
            'debug_mode': True
        })
        return config
    
    @staticmethod
    def get_seawater_test():
        """海水环境测试配置"""
        config = ROVConfig.get_config_dict()
        config.update({
            'object_mass': 0.4,
            'water_density': 1.025,  # 海水密度
            'debug_mode': True
        })
        return config


# ========================================
# 🔧 使用示例
# ========================================

if __name__ == "__main__":
    print("ROV配置文件测试")
    print("=" * 50)
    
    # 显示默认配置
    ROVConfig.print_config_summary()
    
    # 显示预设配置
    print("🎯 预设配置方案:")
    
    presets = [
        ("上浮方块", ROVPresets.get_floating_cube()),
        ("下沉方块", ROVPresets.get_sinking_cube()),
        ("中性浮力", ROVPresets.get_neutral_cube()),
        ("海水测试", ROVPresets.get_seawater_test())
    ]
    
    for name, config in presets:
        density = config['object_mass'] / config['object_volume']
        water_density = config['water_density']
        behavior = "上浮⬆️" if density < water_density else "下沉⬇️" if density > water_density else "悬浮⚖️"
        print(f"   {name}: 密度={density:.2f}, 水密度={water_density:.2f} → {behavior}")
