#!/usr/bin/env python3
"""
ROV水下仿真 - Standalone模式 (立方体测试版本)

此版本用于验证ActionGraph到Python的转换是否正确工作。
- 目标文件: ROV_TEST.usd
- 目标对象: /World/Cube (立方体)
- 测试内容: 浮力和阻尼ActionGraph的Python实现

使用Python类替代ActionGraph实现物理仿真，确保转换后的行为与原始ActionGraph一致。
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb

# USD和物理相关导入
try:
    from pxr import UsdPhysics, Usd
except ImportError:
    carb.log_warn("无法导入pxr模块，某些功能可能受限")

# 导入我们的ROV类 - 更新为简化的函数接口
from rov_classes import (
    calculate_buoyancy_forces,
    calculate_buoyancy_control, 
    calculate_damping,
    PIDController,
    calculate_controller_output,
    calculate_thruster_forces,
    quaternion_to_euler
)


class ROVSimulation:
    """
    ROV仿真主类 - 立方体测试版本（简化架构）
    """

    def __init__(self):
        """初始化立方体仿真（简化版本）"""
        self.world = None
        self.cube_prim = None
        self.cube_view = None

        # 初始化PID控制器（保持状态）
        self.pid_controller = PIDController()

        # 立方体对象参数（用于测试）
        self.cube_volume = 1.0  # m³ - 立方体体积
        self.cube_height = 1.0  # m - 立方体高度

        # 控制输入（保留用于测试）
        self.joystick_x = 0.0
        self.joystick_y = 0.0
        self.dive_input = 0.0
        
        # 调试控制
        self.debug_counter = 0
        self.debug_interval = 60  # 每60帧输出一次详细调试信息
        
    def setup_world(self):
        """设置仿真世界"""
        # 先不创建World对象，等USD加载后再创建
        pass

    def load_usd_scene(self, usd_path: str):
        """
        加载USD场景文件 - 直接打开方式

        Args:
            usd_path: USD文件的绝对路径
        """
        print("🌊 加载 USD 环境文件...")
        
        if not os.path.exists(usd_path):
            carb.log_error(f"❌ USD文件不存在: {usd_path}")
            return False

        try:
            abs_path = os.path.abspath(usd_path)
            print(f"📍 绝对路径: {abs_path}")
            
            # 关闭当前场景
            context = omni.usd.get_context()
            context.close_stage()
            
            # 直接打开USD文件
            print("🔄 直接打开USD文件作为主场景...")
            success = context.open_stage(abs_path)

            if success:
                print("✅ USD文件已作为主场景打开")

                # 等待几帧让USD完全加载
                for _ in range(3):
                    simulation_app.update()

                # 现在创建World对象（仍然需要它来管理RigidPrimView）
                self.world = World(stage_units_in_meters=1.0)

                # 重置世界以初始化场景
                self.world.reset()
                
                # 获取stage以供后续使用
                self.stage = context.get_stage()
                
                print("🎯 World对象和Stage已准备就绪")
                return True
            else:
                carb.log_error("❌ 打开USD文件失败")
                return False

        except Exception as e:
            carb.log_error(f"❌ 加载USD文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_usd_structure(self):
        """分析USD场景结构，找到所有可用的物理对象"""
        try:
            context = omni.usd.get_context()
            stage = context.get_stage()

            print("🔍 分析USD场景结构，寻找所有可用对象...")

            # 分类收集对象
            available_objects = {
                'rigid_bodies': [],
                'cubes': [],
                'spheres': [],
                'meshes': [],
                'others': []
            }

            total_count = 0
            for prim in stage.Traverse():
                total_count += 1
                prim_path = prim.GetPath().pathString
                prim_type = prim.GetTypeName()

                # 跳过根节点
                if prim_path in ["/", "/World"]:
                    continue

                try:
                    # 检查物理属性
                    has_rigid_body = prim.HasAPI(UsdPhysics.RigidBodyAPI) if 'UsdPhysics' in globals() else False
                    has_collider = prim.HasAPI(UsdPhysics.CollisionAPI) if 'UsdPhysics' in globals() else False
                    
                    # 检查几何类型
                    is_cube = prim.IsA(UsdGeom.Cube) if 'UsdGeom' in globals() else "cube" in prim_path.lower()
                    is_sphere = prim.IsA(UsdGeom.Sphere) if 'UsdGeom' in globals() else "sphere" in prim_path.lower()
                    is_mesh = prim.IsA(UsdGeom.Mesh) if 'UsdGeom' in globals() else False

                    obj_info = {
                        'path': prim_path,
                        'type': prim_type,
                        'has_rigid_body': has_rigid_body,
                        'has_collider': has_collider
                    }

                    # 分类对象
                    if has_rigid_body:
                        available_objects['rigid_bodies'].append(obj_info)
                        print(f"  🎯 刚体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_cube:
                        available_objects['cubes'].append(obj_info)
                        print(f"  📦 立方体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_sphere:
                        available_objects['spheres'].append(obj_info)
                        print(f"  🔵 球体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_mesh:
                        available_objects['meshes'].append(obj_info)
                        print(f"  🔷 网格: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif prim_type:  # 只记录有类型的对象
                        available_objects['others'].append(obj_info)

                except Exception as e:
                    # 单个对象检查失败不影响整体
                    pass

            # 输出统计信息
            print(f"\n📊 场景分析结果 (总计{total_count}个prim):")
            print(f"  刚体对象: {len(available_objects['rigid_bodies'])}个")
            print(f"  立方体: {len(available_objects['cubes'])}个") 
            print(f"  球体: {len(available_objects['spheres'])}个")
            print(f"  网格: {len(available_objects['meshes'])}个")
            print(f"  其他对象: {len(available_objects['others'])}个")

            # 自动选择最佳目标
            return self.select_best_target(available_objects)

        except Exception as e:
            carb.log_error(f"分析USD结构失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def select_best_target(self, available_objects):
        """从可用对象中选择最佳的物理目标"""
        print("\n🎯 自动选择物理目标对象...")
        
        # 按优先级选择：刚体 > 立方体 > 球体 > 网格
        if available_objects['rigid_bodies']:
            target = available_objects['rigid_bodies'][0]
            print(f"✅ 选择刚体对象: {target['path']}")
            return [target['path']]
        elif available_objects['cubes']:
            target = available_objects['cubes'][0]
            print(f"✅ 选择立方体对象: {target['path']}")
            return [target['path']]
        elif available_objects['spheres']:
            target = available_objects['spheres'][0]
            print(f"✅ 选择球体对象: {target['path']}")
            return [target['path']]
        elif available_objects['meshes']:
            target = available_objects['meshes'][0]
            print(f"✅ 选择网格对象: {target['path']}")
            return [target['path']]
        else:
            carb.log_error("❌ 未找到合适的物理目标对象")
            # 返回一些常见路径作为备选
            return ["/World/Cube", "/World/Sphere", "/World/defaultRigidBody"]

    def setup_physics_object(self):
        """设置物理对象 - 自动选择最合适的对象"""
        try:
            # 先分析USD结构，获取可用对象列表
            target_paths = self.analyze_usd_structure()
            
            if not target_paths:
                carb.log_error("❌ 未找到任何可用的物理对象")
                return False

            # 尝试第一个推荐的对象
            target_path = target_paths[0]
            print(f"🎯 设置物理对象: {target_path}")

            # 检查对象是否存在
            target_prim = get_prim_at_path(target_path)
            if not target_prim or not target_prim.IsValid():
                carb.log_error(f"❌ 目标对象不存在或无效: {target_path}")
                
                # 尝试其他路径
                for backup_path in target_paths[1:]:
                    print(f"🔄 尝试备选对象: {backup_path}")
                    backup_prim = get_prim_at_path(backup_path)
                    if backup_prim and backup_prim.IsValid():
                        target_path = backup_path
                        target_prim = backup_prim
                        print(f"✅ 找到可用的备选对象: {target_path}")
                        break
                else:
                    carb.log_error("❌ 所有候选对象都不可用")
                    return False

            print(f"✅ 确认目标对象: {target_path}")

            # 创建RigidPrimView
            self.target_view = RigidPrimView(
                prim_paths_expr=target_path,
                name="target_physics_view"
            )

            # 添加到场景
            self.world.scene.add(self.target_view)

            # 验证是否成功创建
            if self.target_view.count > 0:
                print(f"✅ 成功创建物理对象视图，对象数量: {self.target_view.count}")

                # 设置对象引用
                self.target_prim = target_prim
                self.target_path = target_path
                
                # 保持向后兼容性
                self.cube_view = self.target_view
                self.cube_prim = self.target_prim
                self.rov_view = self.target_view
                self.rov_prim = self.target_prim

                print(f"🎉 物理对象设置成功: {target_path}")
                return True
            else:
                carb.log_error("❌ 物理对象视图创建失败，没有找到对象")
                return False

        except Exception as e:
            carb.log_error(f"❌ 设置物理对象失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_rov_state(self):
        """获取ROV当前状态"""
        if self.rov_view is None:
            carb.log_warn("rov_view为None，返回默认状态")
            # 返回默认状态以避免程序崩溃
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }

        try:
            # 获取位置和旋转
            poses = self.rov_view.get_world_poses()
            positions = poses[0]
            orientations = poses[1]

            if positions is not None and len(positions) > 0:
                position = positions[0]
                orientation = orientations[0]

                return {
                    'position': position,
                    'orientation': orientation,
                    'z_position': float(position[2])
                }
            else:
                carb.log_warn("未获取到有效的位置数据，使用默认值")
                return {
                    'position': np.array([0.0, 0.0, 0.0]),
                    'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                    'z_position': 0.0
                }

        except Exception as e:
            carb.log_warn(f"获取ROV状态失败: {e}，使用默认值")
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }
    
    def compute_forces(self, rov_state):
        """计算ROV受力 - 简化版本"""
        if rov_state is None:
            return None
        
        try:
            # 调试控制
            self.debug_counter += 1
            show_debug = (self.debug_counter % self.debug_interval == 0)
            
            if show_debug:
                print(f"\n🔍 详细调试信息 (帧 {self.debug_counter}):")
                print(f"📍 ROV状态: 位置=({rov_state['position'][0]:.3f}, {rov_state['position'][1]:.3f}, {rov_state['z_position']:.3f})")
            
            # 转换四元数到欧拉角 - 简化调用
            quat_xyzw = [
                float(rov_state['orientation'][0]),
                float(rov_state['orientation'][1]), 
                float(rov_state['orientation'][2]),
                float(rov_state['orientation'][3])
            ]
            
            euler_result = quaternion_to_euler(quat_xyzw)
            rotation = euler_result['rotation']
            
            if show_debug:
                print(f"📐 欧拉角 (度): Roll={rotation[0]:.2f}, Pitch={rotation[1]:.2f}, Yaw={rotation[2]:.2f}")
            
            # 计算浮力 - 简化调用
            if show_debug:
                print(f"🌊 计算浮力: 体积={self.cube_volume}m³, 高度={self.cube_height}m, Z位置={rov_state['z_position']:.3f}m")
            
            buoyancy_result = calculate_buoyancy_forces(
                volume=self.cube_volume,
                height=self.cube_height,
                z_position=rov_state['z_position'],
                rotation=rotation,
                debug=show_debug
            )
            
            if show_debug:
                print(f"🎈 浮力输出: X={buoyancy_result['x_force']:.2f}N, Y={buoyancy_result['y_force']:.2f}N, Z={buoyancy_result['z_force']:.2f}N")

            # 计算阻尼 - 简化调用
            damping_result = calculate_damping(
                z_position=rov_state['z_position'],
                max_damping=2.0,
                floating_obj_height=self.cube_height
            )
            
            if show_debug:
                print(f"🌀 阻尼输出: 线性={damping_result['linear_damping']:.3f}, 角度={damping_result['angular_damping']:.3f}")
            
            # 计算推进器控制 - 简化调用
            thruster_result = calculate_thruster_forces(
                y_stick=self.joystick_y,
                x_stick=self.joystick_x
            )
            
            # 计算PID控制 - 使用实例方法
            pitch_angle = rotation[1] if len(rotation) > 1 else 0.0
            controller_result = self.pid_controller.calculate_control(
                orientation=pitch_angle,
                dive_force=self.dive_input
            )
            
            return {
                'buoyancy': buoyancy_result,
                'damping': damping_result,
                'thruster': thruster_result,
                'controller': controller_result,
                'rotation': rotation
            }
            
        except Exception as e:
            print(f"❌ 力计算失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def apply_forces(self, forces):
        """应用计算出的力到ROV"""
        if self.rov_view is None:
            if self.debug_counter % self.debug_interval == 0:
                print("⚠️ rov_view为None，无法应用力")
            return

        if forces is None:
            if self.debug_counter % self.debug_interval == 0:
                print("⚠️ forces为None，跳过力应用")
            return

        show_debug = (self.debug_counter % self.debug_interval == 0)

        try:
            if show_debug:
                print("🔧 开始组合和应用力...")
            
             # 组合所有力到世界坐标系
            total_force_world = np.array([0.0, 0.0, 0.0])

            # 1. 添加浮力（已经在世界坐标系中）
            buoyancy = forces.get('buoyancy', {})
            buoyancy_force_world = np.array([
                buoyancy.get('x_force', 0.0),
                buoyancy.get('y_force', 0.0),
                buoyancy.get('z_force', 0.0)
            ])
            total_force_world += buoyancy_force_world
            if show_debug:
                print(f"🎈 浮力贡献(世界坐标): [{buoyancy_force_world[0]:.2f}, {buoyancy_force_world[1]:.2f}, {buoyancy_force_world[2]:.2f}]N")

            # 2. 转换推进器力从物体本地坐标系到世界坐标系
            thruster = forces.get('thruster', {})
            left_front = thruster.get('left_front', [0, 0, 0])
            right_front = thruster.get('right_front', [0, 0, 0])
            left_back = thruster.get('left_back', [0, 0, 0])
            right_back = thruster.get('right_back', [0, 0, 0])

            # 合并推进器力（物体本地坐标系）
            thruster_force_local = np.array(left_front) + np.array(right_front) + \
                                 np.array(left_back) + np.array(right_back)
            
            # 转换推进器力到世界坐标系
            thruster_force_world = self._transform_local_to_world_force(thruster_force_local, forces.get('rotation', [0, 0, 0]))
            total_force_world += thruster_force_world
            
            if show_debug and np.any(thruster_force_local != 0):
                print(f"🚀 推进器贡献(本地坐标): [{thruster_force_local[0]:.2f}, {thruster_force_local[1]:.2f}, {thruster_force_local[2]:.2f}]N")
                print(f"🚀 推进器贡献(世界坐标): [{thruster_force_world[0]:.2f}, {thruster_force_world[1]:.2f}, {thruster_force_world[2]:.2f}]N")

            # 3. 转换控制器力从物体本地坐标系到世界坐标系
            controller = forces.get('controller', {})
            controller_force = controller.get('force', [0, 0, 0])
            if isinstance(controller_force, (list, tuple, np.ndarray)) and len(controller_force) >= 3:
                controller_force_local = np.array(controller_force[:3])
                
                # 转换控制器力到世界坐标系
                controller_force_world = self._transform_local_to_world_force(controller_force_local, forces.get('rotation', [0, 0, 0]))
                total_force_world += controller_force_world
                
                if show_debug and np.any(controller_force_local != 0):
                    print(f"🎮 控制器贡献(本地坐标): [{controller_force_local[0]:.2f}, {controller_force_local[1]:.2f}, {controller_force_local[2]:.2f}]N")
                    print(f"🎮 控制器贡献(世界坐标): [{controller_force_world[0]:.2f}, {controller_force_world[1]:.2f}, {controller_force_world[2]:.2f}]N")

            # 检查总力的合理性
            total_magnitude = np.linalg.norm(total_force_world)
            if show_debug:
                print(f"⚡ 合成总力(世界坐标): [{total_force_world[0]:.2f}, {total_force_world[1]:.2f}, {total_force_world[2]:.2f}]N (幅度: {total_magnitude:.2f}N)")
            
            # 检查力是否过大（避免仿真不稳定）
            max_force = 1000.0  # N
            if total_magnitude > max_force:
                if show_debug:
                    print(f"⚠️ 力过大，进行限制: {total_magnitude:.2f}N -> {max_force}N")
                total_force_world = total_force_world / total_magnitude * max_force

            # 应用力（Isaac Sim可能期望世界坐标系的力）
            try:
                # 获取刚体数量
                count = self.rov_view.count
                if show_debug:
                    print(f"🎯 应用力到 {count} 个刚体")

                # 为每个刚体创建相同的力
                if count > 0:
                    forces_tensor = np.tile(total_force_world, (count, 1))
                    self.rov_view.apply_forces(forces_tensor)
                    if show_debug:
                        print(f"✅ 力已成功应用")
                else:
                    if show_debug:
                        print("⚠️ 刚体数量为0，无法应用力")

            except Exception as e:
                if show_debug:
                    print(f"⚠️ 获取刚体数量失败: {e}，尝试直接应用力")
                # 尝试简单的单个力应用
                forces_tensor = total_force_world.reshape(1, 3)
                self.rov_view.apply_forces(forces_tensor)
                if show_debug:
                    print(f"✅ 使用单个力应用成功")

        except Exception as e:
            print(f"❌ 应用力失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _transform_local_to_world_force(self, local_force, rotation_degrees):
        """
        将物体本地坐标系的力转换到世界坐标系
        
        Args:
            local_force: 物体本地坐标系的力向量 [x,y,z]
            rotation_degrees: 物体的旋转角度 [roll, pitch, yaw] (度)
        
        Returns:
            世界坐标系的力向量
        """
        if np.all(local_force == 0):
            return np.array([0.0, 0.0, 0.0])
        
        try:
            # 转换角度为弧度
            rotation_radians = np.deg2rad(rotation_degrees)
            
            # 创建旋转矩阵（从本地到世界）
            roll_matrix = self._create_rotation_matrix_x(rotation_radians[0])
            pitch_matrix = self._create_rotation_matrix_y(rotation_radians[1])
            yaw_matrix = self._create_rotation_matrix_z(rotation_radians[2])
            
            # 组合旋转矩阵
            rotation_matrix = roll_matrix @ pitch_matrix @ yaw_matrix
            
            # 应用旋转变换
            local_force_vec = np.array(local_force).reshape(3, 1)
            world_force_vec = rotation_matrix @ local_force_vec
            
            return world_force_vec.flatten()
            
        except Exception as e:
            print(f"⚠️ 力坐标变换失败: {e}")
            return np.array(local_force)  # 返回原始值作为备选
    
    def _create_rotation_matrix_x(self, angle):
        """创建绕X轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [1, 0, 0],
            [0, c, -s],
            [0, s, c]
        ])
    
    def _create_rotation_matrix_y(self, angle):
        """创建绕Y轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, 0, s],
            [0, 1, 0],
            [-s, 0, c]
        ])
    
    def _create_rotation_matrix_z(self, angle):
        """创建绕Z轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, -s, 0],
            [s, c, 0],
            [0, 0, 1]
        ])
    
    def update_controls(self):
        """更新控制输入（这里可以添加键盘/手柄输入）"""
        # 简单的键盘控制示例
        # 实际使用中可以集成手柄输入
        pass
    
    def step(self):
        """仿真步进"""
        if self.world is None or self.rov_view is None:
            return

        # 更新控制输入
        self.update_controls()

        # 获取ROV状态
        rov_state = self.get_rov_state()

        # 计算力
        forces = self.compute_forces(rov_state)

        # 应用力
        self.apply_forces(forces)

        # 步进仿真
        self.world.step(render=True)


def main():
    """主函数"""
    # 创建ROV仿真实例
    rov_sim = ROVSimulation()

    # 加载USD场景 - 使用新的测试文件ROV_TEST.usd
    usd_path = os.path.abspath("/home/<USER>/self_underwater_isaacsim/BUOYANCY_TEST.usd")
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return

    # 设置物理对象（自动选择最合适的对象）
    if not rov_sim.setup_physics_object():
        carb.log_error("❌ 无法设置物理对象，退出")
        simulation_app.close()
        return

    print("🎉 物理仿真启动成功，开始仿真循环...")

    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        print("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
