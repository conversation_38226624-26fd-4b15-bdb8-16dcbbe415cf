#!/usr/bin/env python3
"""
ROV水下仿真 - Standalone模式 (立方体测试版本)

此版本用于验证ActionGraph到Python的转换是否正确工作。
- 目标文件: ROV_TEST.usd
- 目标对象: /World/Cube (立方体)
- 测试内容: 浮力和阻尼ActionGraph的Python实现

使用Python类替代ActionGraph实现物理仿真，确保转换后的行为与原始ActionGraph一致。
"""

# Isaac Sim标准初始化代码
from isaacsim import SimulationApp
simulation_app = SimulationApp({"headless": False})

import os
import numpy as np
import omni.usd
from omni.isaac.core import World
from omni.isaac.core.utils.stage import add_reference_to_stage
from omni.isaac.core.utils.prims import get_prim_at_path
from omni.isaac.core.prims import RigidPrimView
import carb

# USD和物理相关导入
try:
    from pxr import UsdPhysics, Usd, UsdGeom
except ImportError:
    carb.log_warn("无法导入pxr模块，某些功能可能受限")

# 导入我们的ROV类 - 更新为简化的函数接口
from rov_classes import (
    calculate_buoyancy_forces,
    calculate_buoyancy_control, 
    calculate_damping,
    PIDController,
    calculate_controller_output,
    calculate_thruster_forces,
    quaternion_to_euler
)


class ROVSimulation:
    """
    ROV仿真主类 - 立方体测试版本（简化架构）
    """

    def __init__(self):
        """初始化立方体仿真（简化版本）"""
        self.world = None
        self.cube_prim = None
        self.cube_view = None

        # 初始化PID控制器（保持状态）- 使用更保守的参数
        self.pid_controller = PIDController(
            kp=10.0,    # 降低比例增益，原来100
            ki=1.0,     # 降低积分增益，原来10
            kd=0.1,     # 增加微分增益，原来0.01
            sat_max=50.0,  # 降低饱和限制，原来1000
            sat_min=-50.0  # 降低饱和限制，原来-1000
        )

        # 立方体对象参数（用于测试）
        self.cube_volume = 1.0  # m³ - 立方体体积
        self.cube_height = 1.0  # m - 立方体高度

        # 控制输入（保留用于测试）
        self.joystick_x = 0.0
        self.joystick_y = 0.0
        self.dive_input = 0.0

        # 配置参数（将在main中设置）
        self.config = {
            'water_density': 1.0,
            'gravity': 9.8,
            'object_mass': 0.4,
            'max_damping': 2.0,
            'max_buoyancy_force': 50.0,
            'max_thruster_force': 20.0,
            'max_controller_force': 30.0,
            'max_total_force': 100.0,
            'debug_mode': True
        }

        # 调试控制
        self.debug_counter = 0
        self.debug_interval = 60  # 每60帧输出一次详细调试信息

    def apply_config(self, config_dict):
        """应用配置参数到仿真实例"""
        self.config.update(config_dict)

        # 更新物体属性
        if 'object_volume' in config_dict:
            self.cube_volume = config_dict['object_volume']
        if 'object_height' in config_dict:
            self.cube_height = config_dict['object_height']

        # 更新PID控制器
        if any(key in config_dict for key in ['pid_kp', 'pid_ki', 'pid_kd', 'pid_sat_max', 'pid_sat_min']):
            self.pid_controller = PIDController(
                kp=config_dict.get('pid_kp', 10.0),
                ki=config_dict.get('pid_ki', 1.0),
                kd=config_dict.get('pid_kd', 0.1),
                sat_max=config_dict.get('pid_sat_max', 50.0),
                sat_min=config_dict.get('pid_sat_min', -50.0)
            )

        print(f"✅ 配置已应用: {len(config_dict)} 个参数")
        
    def setup_world(self):
        """设置仿真世界"""
        # 先不创建World对象，等USD加载后再创建
        pass

    def load_usd_scene(self, usd_path: str):
        """
        加载USD场景文件 - 直接打开方式

        Args:
            usd_path: USD文件的绝对路径
        """
        print("🌊 加载 USD 环境文件...")
        
        if not os.path.exists(usd_path):
            carb.log_error(f"❌ USD文件不存在: {usd_path}")
            return False

        try:
            abs_path = os.path.abspath(usd_path)
            print(f"📍 绝对路径: {abs_path}")
            
            # 关闭当前场景
            context = omni.usd.get_context()
            context.close_stage()
            
            # 直接打开USD文件
            print("🔄 直接打开USD文件作为主场景...")
            success = context.open_stage(abs_path)

            if success:
                print("✅ USD文件已作为主场景打开")

                # 等待几帧让USD完全加载
                for _ in range(3):
                    simulation_app.update()

                # 现在创建World对象（仍然需要它来管理RigidPrimView）
                self.world = World(stage_units_in_meters=1.0)

                # 🚨 关键修复：禁用Isaac Sim的自动重力，我们手动控制浮力和重力平衡
                try:
                    # 获取物理场景并禁用重力
                    physics_context = self.world.get_physics_context()
                    if physics_context:
                        # 设置重力为0，我们通过浮力计算来手动控制重力效果
                        physics_context.set_gravity(0.0)
                        print("✅ 已禁用Isaac Sim自动重力，使用手动浮力控制")
                    else:
                        print("⚠️ 无法获取物理上下文，重力设置可能无效")
                except Exception as e:
                    print(f"⚠️ 重力设置失败: {e}")

                # 重置世界以初始化场景
                self.world.reset()
                
                # 获取stage以供后续使用
                self.stage = context.get_stage()
                
                print("🎯 World对象和Stage已准备就绪")
                return True
            else:
                carb.log_error("❌ 打开USD文件失败")
                return False

        except Exception as e:
            carb.log_error(f"❌ 加载USD文件失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_usd_structure(self):
        """分析USD场景结构，找到所有可用的物理对象"""
        try:
            context = omni.usd.get_context()
            stage = context.get_stage()

            print("🔍 分析USD场景结构，寻找所有可用对象...")

            # 分类收集对象
            available_objects = {
                'rigid_bodies': [],
                'cubes': [],
                'spheres': [],
                'meshes': [],
                'others': []
            }

            total_count = 0
            for prim in stage.Traverse():
                total_count += 1
                prim_path = prim.GetPath().pathString
                prim_type = prim.GetTypeName()

                # 跳过根节点
                if prim_path in ["/", "/World"]:
                    continue

                try:
                    # 检查物理属性
                    has_rigid_body = prim.HasAPI(UsdPhysics.RigidBodyAPI) if 'UsdPhysics' in globals() else False
                    has_collider = prim.HasAPI(UsdPhysics.CollisionAPI) if 'UsdPhysics' in globals() else False
                    
                    # 检查几何类型
                    is_cube = prim.IsA(UsdGeom.Cube) if 'UsdGeom' in globals() else "cube" in prim_path.lower()
                    is_sphere = prim.IsA(UsdGeom.Sphere) if 'UsdGeom' in globals() else "sphere" in prim_path.lower()
                    is_mesh = prim.IsA(UsdGeom.Mesh) if 'UsdGeom' in globals() else False

                    obj_info = {
                        'path': prim_path,
                        'type': prim_type,
                        'has_rigid_body': has_rigid_body,
                        'has_collider': has_collider
                    }

                    # 分类对象
                    if has_rigid_body:
                        available_objects['rigid_bodies'].append(obj_info)
                        print(f"  🎯 刚体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_cube:
                        available_objects['cubes'].append(obj_info)
                        print(f"  📦 立方体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_sphere:
                        available_objects['spheres'].append(obj_info)
                        print(f"  🔵 球体: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif is_mesh:
                        available_objects['meshes'].append(obj_info)
                        print(f"  🔷 网格: {prim_path} ({prim_type}) - 碰撞器:{has_collider}")
                    elif prim_type:  # 只记录有类型的对象
                        available_objects['others'].append(obj_info)

                except Exception as e:
                    # 单个对象检查失败不影响整体
                    pass

            # 输出统计信息
            print(f"\n📊 场景分析结果 (总计{total_count}个prim):")
            print(f"  刚体对象: {len(available_objects['rigid_bodies'])}个")
            print(f"  立方体: {len(available_objects['cubes'])}个") 
            print(f"  球体: {len(available_objects['spheres'])}个")
            print(f"  网格: {len(available_objects['meshes'])}个")
            print(f"  其他对象: {len(available_objects['others'])}个")

            # 自动选择最佳目标
            return self.select_best_target(available_objects)

        except Exception as e:
            carb.log_error(f"分析USD结构失败: {e}")
            import traceback
            traceback.print_exc()
            return []

    def select_best_target(self, available_objects):
        """从可用对象中选择最佳的物理目标"""
        print("\n🎯 自动选择物理目标对象...")
        
        # 按优先级选择：刚体 > 立方体 > 球体 > 网格
        if available_objects['rigid_bodies']:
            target = available_objects['rigid_bodies'][0]
            print(f"✅ 选择刚体对象: {target['path']}")
            return [target['path']]
        elif available_objects['cubes']:
            target = available_objects['cubes'][0]
            print(f"✅ 选择立方体对象: {target['path']}")
            return [target['path']]
        elif available_objects['spheres']:
            target = available_objects['spheres'][0]
            print(f"✅ 选择球体对象: {target['path']}")
            return [target['path']]
        elif available_objects['meshes']:
            target = available_objects['meshes'][0]
            print(f"✅ 选择网格对象: {target['path']}")
            return [target['path']]
        else:
            carb.log_error("❌ 未找到合适的物理目标对象")
            # 返回一些常见路径作为备选
            return ["/World/Cube", "/World/Sphere", "/World/defaultRigidBody"]

    def setup_physics_object(self):
        """设置物理对象 - 自动选择最合适的对象"""
        try:
            # 先分析USD结构，获取可用对象列表
            target_paths = self.analyze_usd_structure()

            if not target_paths:
                carb.log_error("❌ 未找到任何可用的物理对象")
                return False

            # 尝试第一个推荐的对象
            target_path = target_paths[0]
            print(f"🎯 设置物理对象: {target_path}")

            # 检查对象是否存在
            target_prim = get_prim_at_path(target_path)
            if not target_prim or not target_prim.IsValid():
                carb.log_error(f"❌ 目标对象不存在或无效: {target_path}")

                # 尝试其他路径
                for backup_path in target_paths[1:]:
                    print(f"🔄 尝试备选对象: {backup_path}")
                    backup_prim = get_prim_at_path(backup_path)
                    if backup_prim and backup_prim.IsValid():
                        target_path = backup_path
                        target_prim = backup_prim
                        print(f"✅ 找到可用的备选对象: {target_path}")
                        break
                else:
                    carb.log_error("❌ 所有候选对象都不可用")
                    return False

            print(f"✅ 确认目标对象: {target_path}")

            # 🚨 修复: 检查和设置物体质量
            self._check_and_fix_mass(target_prim, target_path)

            # 创建RigidPrimView
            self.target_view = RigidPrimView(
                prim_paths_expr=target_path,
                name="target_physics_view"
            )

            # 添加到场景
            self.world.scene.add(self.target_view)

            # 验证是否成功创建
            if self.target_view.count > 0:
                print(f"✅ 成功创建物理对象视图，对象数量: {self.target_view.count}")

                # 设置对象引用
                self.target_prim = target_prim
                self.target_path = target_path

                # 保持向后兼容性
                self.cube_view = self.target_view
                self.cube_prim = self.target_prim
                self.rov_view = self.target_view
                self.rov_prim = self.target_prim

                print(f"🎉 物理对象设置成功: {target_path}")
                return True
            else:
                carb.log_error("❌ 物理对象视图创建失败，没有找到对象")
                return False

        except Exception as e:
            carb.log_error(f"❌ 设置物理对象失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _check_and_fix_mass(self, prim, prim_path):
        """设置物体质量为0.4kg，使其能够浮在水面"""
        try:
            print(f"🔍 设置物体质量: {prim_path}")

            # 直接设置质量为0.4kg（方块体积1m³，密度0.4kg/m³，小于水的密度1.0kg/m³）
            target_mass = 0.4  # kg

            # 检查是否有质量API
            if 'UsdPhysics' in globals():
                has_mass_api = prim.HasAPI(UsdPhysics.MassAPI)
                print(f"   质量API存在: {has_mass_api}")

                if has_mass_api:
                    mass_api = UsdPhysics.MassAPI(prim)
                    mass_attr = mass_api.GetMassAttr()
                    if mass_attr:
                        current_mass = mass_attr.Get()
                        print(f"   当前质量: {current_mass} kg")

                        # 直接设置为目标质量
                        mass_attr.Set(target_mass)
                        print(f"   ✅ 质量已设置为: {target_mass} kg")

                        # 验证设置
                        new_mass = mass_attr.Get()
                        print(f"   ✅ 验证质量: {new_mass} kg")
                    else:
                        print("   ⚠️ 未找到质量属性，创建新的")
                        mass_api.CreateMassAttr(target_mass)
                        print(f"   ✅ 已创建质量属性: {target_mass} kg")
                else:
                    # 添加质量API
                    print("   🔧 添加质量API...")
                    mass_api = UsdPhysics.MassAPI.Apply(prim)
                    mass_api.CreateMassAttr(target_mass)
                    print(f"   ✅ 已添加质量API，质量设为: {target_mass} kg")

                # 计算理论浮力和重力
                volume = self.cube_volume  # 1.0 m³
                water_density = 1.0  # kg/m³
                gravity = 9.8  # m/s²

                buoyancy_force = water_density * volume * gravity  # 向上
                weight_force = target_mass * gravity  # 向下
                net_force = buoyancy_force - weight_force

                print(f"   📊 力分析:")
                print(f"      浮力: {buoyancy_force:.2f}N (向上)")
                print(f"      重力: {weight_force:.2f}N (向下)")
                print(f"      净力: {net_force:.2f}N ({'向上' if net_force > 0 else '向下'})")

                if net_force > 0:
                    print(f"   ✅ 物体应该上浮")
                else:
                    print(f"   ⚠️ 物体会下沉")

            else:
                print("   ⚠️ UsdPhysics模块不可用，无法设置质量")

        except Exception as e:
            print(f"   ⚠️ 质量设置失败: {e}")
            import traceback
            traceback.print_exc()
    
    def get_rov_state(self):
        """获取ROV当前状态"""
        if self.rov_view is None:
            carb.log_warn("rov_view为None，返回默认状态")
            # 返回默认状态以避免程序崩溃
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }

        try:
            # 获取位置和旋转
            poses = self.rov_view.get_world_poses()
            positions = poses[0]
            orientations = poses[1]

            if positions is not None and len(positions) > 0:
                position = positions[0]
                orientation = orientations[0]

                return {
                    'position': position,
                    'orientation': orientation,
                    'z_position': float(position[2])
                }
            else:
                carb.log_warn("未获取到有效的位置数据，使用默认值")
                return {
                    'position': np.array([0.0, 0.0, 0.0]),
                    'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                    'z_position': 0.0
                }

        except Exception as e:
            carb.log_warn(f"获取ROV状态失败: {e}，使用默认值")
            return {
                'position': np.array([0.0, 0.0, 0.0]),
                'orientation': np.array([0.0, 0.0, 0.0, 1.0]),
                'z_position': 0.0
            }
    
    def compute_forces(self, rov_state):
        """计算ROV受力 - 简化版本"""
        if rov_state is None:
            return None
        
        try:
            # 调试控制
            self.debug_counter += 1
            show_debug = (self.debug_counter % self.debug_interval == 0)
            
            if show_debug:
                print(f"\n🔍 详细调试信息 (帧 {self.debug_counter}):")
                print(f"📍 ROV状态: 位置=({rov_state['position'][0]:.3f}, {rov_state['position'][1]:.3f}, {rov_state['z_position']:.3f})")
            
            # 转换四元数到欧拉角 - 简化调用
            quat_xyzw = [
                float(rov_state['orientation'][0]),
                float(rov_state['orientation'][1]), 
                float(rov_state['orientation'][2]),
                float(rov_state['orientation'][3])
            ]
            
            euler_result = quaternion_to_euler(quat_xyzw)
            rotation = euler_result['rotation']
            
            if show_debug:
                print(f"📐 欧拉角 (度): Roll={rotation[0]:.2f}, Pitch={rotation[1]:.2f}, Yaw={rotation[2]:.2f}")
            
            # 计算浮力 - 简化调用
            if show_debug:
                print(f"🌊 计算浮力: 体积={self.cube_volume}m³, 高度={self.cube_height}m, Z位置={rov_state['z_position']:.3f}m")
            
            buoyancy_result = calculate_buoyancy_forces(
                volume=self.cube_volume,
                height=self.cube_height,
                z_position=rov_state['z_position'],
                rotation=rotation,
                water_density=self.config['water_density'],
                gravity=self.config['gravity'],
                object_mass=self.config['object_mass'],
                debug=show_debug
            )
            
            if show_debug:
                print(f"🎈 浮力输出: X={buoyancy_result['x_force']:.2f}N, Y={buoyancy_result['y_force']:.2f}N, Z={buoyancy_result['z_force']:.2f}N")

            # 计算阻尼 - 使用配置参数
            damping_result = calculate_damping(
                z_position=rov_state['z_position'],
                max_damping=self.config['max_damping'],
                floating_obj_height=self.cube_height
            )
            
            if show_debug:
                print(f"🌀 阻尼输出: 线性={damping_result['linear_damping']:.3f}, 角度={damping_result['angular_damping']:.3f}")
            
            # 计算推进器控制 - 简化调用
            thruster_result = calculate_thruster_forces(
                y_stick=self.joystick_y,
                x_stick=self.joystick_x
            )
            
            # 计算PID控制 - 使用实例方法
            pitch_angle = rotation[1] if len(rotation) > 1 else 0.0
            controller_result = self.pid_controller.calculate_control(
                orientation=pitch_angle,
                dive_force=self.dive_input
            )
            
            return {
                'buoyancy': buoyancy_result,
                'damping': damping_result,
                'thruster': thruster_result,
                'controller': controller_result,
                'rotation': rotation
            }
            
        except Exception as e:
            print(f"❌ 力计算失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def apply_forces(self, forces):
        """应用计算出的力到ROV - 修复版本，防止物体飞走"""
        if self.rov_view is None:
            if self.debug_counter % self.debug_interval == 0:
                print("⚠️ rov_view为None，无法应用力")
            return

        if forces is None:
            if self.debug_counter % self.debug_interval == 0:
                print("⚠️ forces为None，跳过力应用")
            return

        show_debug = (self.debug_counter % self.debug_interval == 0)

        try:
            if show_debug:
                print("🔧 开始组合和应用力...")

            # 组合所有力到世界坐标系
            total_force_world = np.array([0.0, 0.0, 0.0])

            # 1. 添加浮力（已经在世界坐标系中）
            buoyancy = forces.get('buoyancy', {})
            buoyancy_force_world = np.array([
                buoyancy.get('x_force', 0.0),
                buoyancy.get('y_force', 0.0),
                buoyancy.get('z_force', 0.0)
            ])

            # 🚨 修复1: 限制浮力的最大值，防止过大
            buoyancy_magnitude = np.linalg.norm(buoyancy_force_world)
            max_buoyancy = 50.0  # 限制浮力最大50N
            if buoyancy_magnitude > max_buoyancy:
                buoyancy_force_world = buoyancy_force_world / buoyancy_magnitude * max_buoyancy
                if show_debug:
                    print(f"⚠️ 浮力过大，限制为: {max_buoyancy}N")

            total_force_world += buoyancy_force_world
            if show_debug:
                print(f"🎈 浮力贡献(世界坐标): [{buoyancy_force_world[0]:.2f}, {buoyancy_force_world[1]:.2f}, {buoyancy_force_world[2]:.2f}]N")

            # 2. 转换推进器力从物体本地坐标系到世界坐标系
            thruster = forces.get('thruster', {})
            left_front = thruster.get('left_front', [0, 0, 0])
            right_front = thruster.get('right_front', [0, 0, 0])
            left_back = thruster.get('left_back', [0, 0, 0])
            right_back = thruster.get('right_back', [0, 0, 0])

            # 合并推进器力（物体本地坐标系）
            thruster_force_local = np.array(left_front) + np.array(right_front) + \
                                 np.array(left_back) + np.array(right_back)

            # 🚨 修复2: 限制推进器力的最大值
            thruster_magnitude = np.linalg.norm(thruster_force_local)
            max_thruster = 20.0  # 限制推进器力最大20N
            if thruster_magnitude > max_thruster:
                thruster_force_local = thruster_force_local / thruster_magnitude * max_thruster
                if show_debug:
                    print(f"⚠️ 推进器力过大，限制为: {max_thruster}N")

            # 转换推进器力到世界坐标系
            thruster_force_world = self._transform_local_to_world_force(thruster_force_local, forces.get('rotation', [0, 0, 0]))
            total_force_world += thruster_force_world

            if show_debug and np.any(thruster_force_local != 0):
                print(f"🚀 推进器贡献(本地坐标): [{thruster_force_local[0]:.2f}, {thruster_force_local[1]:.2f}, {thruster_force_local[2]:.2f}]N")
                print(f"🚀 推进器贡献(世界坐标): [{thruster_force_world[0]:.2f}, {thruster_force_world[1]:.2f}, {thruster_force_world[2]:.2f}]N")

            # 3. 转换控制器力从物体本地坐标系到世界坐标系
            controller = forces.get('controller', {})
            controller_force = controller.get('force', [0, 0, 0])
            if isinstance(controller_force, (list, tuple, np.ndarray)) and len(controller_force) >= 3:
                controller_force_local = np.array(controller_force[:3])

                # 🚨 修复3: 限制PID控制器力的最大值
                controller_magnitude = np.linalg.norm(controller_force_local)
                max_controller = 30.0  # 限制控制器力最大30N
                if controller_magnitude > max_controller:
                    controller_force_local = controller_force_local / controller_magnitude * max_controller
                    if show_debug:
                        print(f"⚠️ 控制器力过大，限制为: {max_controller}N")

                # 转换控制器力到世界坐标系
                controller_force_world = self._transform_local_to_world_force(controller_force_local, forces.get('rotation', [0, 0, 0]))
                total_force_world += controller_force_world

                if show_debug and np.any(controller_force_local != 0):
                    print(f"🎮 控制器贡献(本地坐标): [{controller_force_local[0]:.2f}, {controller_force_local[1]:.2f}, {controller_force_local[2]:.2f}]N")
                    print(f"🎮 控制器贡献(世界坐标): [{controller_force_world[0]:.2f}, {controller_force_world[1]:.2f}, {controller_force_world[2]:.2f}]N")

            # 🚨 修复4: 最终总力限制，这是最重要的安全措施
            total_magnitude = np.linalg.norm(total_force_world)
            max_total_force = 100.0  # 总力不超过100N
            if total_magnitude > max_total_force:
                if show_debug:
                    print(f"⚠️ 总力过大，进行限制: {total_magnitude:.2f}N -> {max_total_force}N")
                total_force_world = total_force_world / total_magnitude * max_total_force
                total_magnitude = max_total_force

            if show_debug:
                print(f"⚡ 合成总力(世界坐标): [{total_force_world[0]:.2f}, {total_force_world[1]:.2f}, {total_force_world[2]:.2f}]N (幅度: {total_magnitude:.2f}N)")

            # 🚨 修复5: 添加力的合理性检查
            if np.any(np.isnan(total_force_world)) or np.any(np.isinf(total_force_world)):
                print("❌ 检测到无效力值(NaN或Inf)，跳过此帧")
                return

            # 🚨 修复6: 如果力太小，直接跳过以避免数值噪声
            if total_magnitude < 0.001:  # 小于0.001N的力忽略
                if show_debug:
                    print("💤 力太小，跳过应用")
                return

            # 应用力到Isaac Sim
            try:
                # 获取刚体数量
                count = self.rov_view.count
                if show_debug:
                    print(f"🎯 应用力到 {count} 个刚体")

                # 为每个刚体创建相同的力
                if count > 0:
                    forces_tensor = np.tile(total_force_world, (count, 1))
                    self.rov_view.apply_forces(forces_tensor)
                    if show_debug:
                        print(f"✅ 力已成功应用")
                else:
                    if show_debug:
                        print("⚠️ 刚体数量为0，无法应用力")

            except Exception as e:
                if show_debug:
                    print(f"⚠️ 获取刚体数量失败: {e}，尝试直接应用力")
                # 尝试简单的单个力应用
                forces_tensor = total_force_world.reshape(1, 3)
                self.rov_view.apply_forces(forces_tensor)
                if show_debug:
                    print(f"✅ 使用单个力应用成功")

        except Exception as e:
            print(f"❌ 应用力失败: {e}")
            import traceback
            traceback.print_exc()
    
    def _transform_local_to_world_force(self, local_force, rotation_degrees):
        """
        将物体本地坐标系的力转换到世界坐标系
        
        Args:
            local_force: 物体本地坐标系的力向量 [x,y,z]
            rotation_degrees: 物体的旋转角度 [roll, pitch, yaw] (度)
        
        Returns:
            世界坐标系的力向量
        """
        if np.all(local_force == 0):
            return np.array([0.0, 0.0, 0.0])
        
        try:
            # 转换角度为弧度
            rotation_radians = np.deg2rad(rotation_degrees)
            
            # 创建旋转矩阵（从本地到世界）
            roll_matrix = self._create_rotation_matrix_x(rotation_radians[0])
            pitch_matrix = self._create_rotation_matrix_y(rotation_radians[1])
            yaw_matrix = self._create_rotation_matrix_z(rotation_radians[2])
            
            # 组合旋转矩阵
            rotation_matrix = roll_matrix @ pitch_matrix @ yaw_matrix
            
            # 应用旋转变换
            local_force_vec = np.array(local_force).reshape(3, 1)
            world_force_vec = rotation_matrix @ local_force_vec
            
            return world_force_vec.flatten()
            
        except Exception as e:
            print(f"⚠️ 力坐标变换失败: {e}")
            return np.array(local_force)  # 返回原始值作为备选
    
    def _create_rotation_matrix_x(self, angle):
        """创建绕X轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [1, 0, 0],
            [0, c, -s],
            [0, s, c]
        ])
    
    def _create_rotation_matrix_y(self, angle):
        """创建绕Y轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, 0, s],
            [0, 1, 0],
            [-s, 0, c]
        ])
    
    def _create_rotation_matrix_z(self, angle):
        """创建绕Z轴的旋转矩阵"""
        c = np.cos(angle)
        s = np.sin(angle)
        return np.array([
            [c, -s, 0],
            [s, c, 0],
            [0, 0, 1]
        ])
    
    def update_controls(self):
        """更新控制输入（这里可以添加键盘/手柄输入）"""
        # 简单的键盘控制示例
        # 实际使用中可以集成手柄输入
        pass
    
    def step(self):
        """仿真步进"""
        if self.world is None or self.rov_view is None:
            return

        # 更新控制输入
        self.update_controls()

        # 获取ROV状态
        rov_state = self.get_rov_state()

        # 计算力
        forces = self.compute_forces(rov_state)

        # 应用力
        self.apply_forces(forces)

        # 步进仿真
        self.world.step(render=True)


def main():
    """主函数 - 统一参数配置"""

    # ========================================
    # 🎛️ 统一参数配置区域 - 在这里修改所有重要参数
    # ========================================

    # 📁 文件路径配置
    USD_FILE_PATH = "/home/<USER>/self_underwater_isaacsim/BUOYANCY_TEST.usd"

    # 🧊 物体物理属性
    OBJECT_MASS = 0.4           # kg - 物体质量（密度 = 质量/体积）
    OBJECT_VOLUME = 1.0         # m³ - 物体体积
    OBJECT_HEIGHT = 1.0         # m - 物体高度

    # 🌊 水环境参数
    WATER_DENSITY = 1.0         # kg/m³ - 水密度（1.0=淡水，1.025=海水）
    GRAVITY = 9.8               # m/s² - 重力加速度

    # 🎮 控制器参数
    PID_KP = 10.0               # 比例增益
    PID_KI = 1.0                # 积分增益
    PID_KD = 0.1                # 微分增益
    PID_SAT_MAX = 50.0          # 控制器输出上限
    PID_SAT_MIN = -50.0         # 控制器输出下限

    # 🌀 阻尼参数
    MAX_DAMPING = 2.0           # 最大阻尼系数

    # 🚀 力限制参数（安全保护）
    MAX_BUOYANCY_FORCE = 50.0   # N - 最大浮力
    MAX_THRUSTER_FORCE = 20.0   # N - 最大推进器力
    MAX_CONTROLLER_FORCE = 30.0 # N - 最大控制器力
    MAX_TOTAL_FORCE = 100.0     # N - 最大总力

    # 🔧 仿真参数
    ENABLE_DEBUG = True         # 是否显示调试信息
    DISABLE_ISAAC_GRAVITY = True # 是否禁用Isaac Sim自动重力

    # 📊 理论计算验证
    object_density = OBJECT_MASS / OBJECT_VOLUME
    print(f"📊 参数验证:")
    print(f"   物体密度: {object_density:.2f} kg/m³")
    print(f"   水密度: {WATER_DENSITY:.2f} kg/m³")
    print(f"   理论浮力: {WATER_DENSITY * OBJECT_VOLUME * GRAVITY:.2f}N (向上)")
    print(f"   理论重力: {OBJECT_MASS * GRAVITY:.2f}N (向下)")
    print(f"   理论净力: {(WATER_DENSITY * OBJECT_VOLUME - OBJECT_MASS) * GRAVITY:.2f}N")
    print(f"   预期行为: {'上浮 ⬆️' if object_density < WATER_DENSITY else '下沉 ⬇️' if object_density > WATER_DENSITY else '悬浮 ⚖️'}")
    print()

    # ========================================
    # 🚀 仿真初始化和运行
    # ========================================

    # 创建ROV仿真实例
    rov_sim = ROVSimulation()

    # 应用配置参数到仿真实例
    config_to_apply = {
        'object_volume': OBJECT_VOLUME,
        'object_height': OBJECT_HEIGHT,
        'object_mass': OBJECT_MASS,
        'water_density': WATER_DENSITY,
        'gravity': GRAVITY,
        'pid_kp': PID_KP,
        'pid_ki': PID_KI,
        'pid_kd': PID_KD,
        'pid_sat_max': PID_SAT_MAX,
        'pid_sat_min': PID_SAT_MIN,
        'max_damping': MAX_DAMPING,
        'max_buoyancy_force': MAX_BUOYANCY_FORCE,
        'max_thruster_force': MAX_THRUSTER_FORCE,
        'max_controller_force': MAX_CONTROLLER_FORCE,
        'max_total_force': MAX_TOTAL_FORCE,
        'debug_mode': ENABLE_DEBUG
    }

    rov_sim.apply_config(config_to_apply)

    print(f"✅ 参数配置完成，开始加载场景...")

    # 加载USD场景
    usd_path = os.path.abspath(USD_FILE_PATH)
    if not rov_sim.load_usd_scene(usd_path):
        carb.log_error("无法加载USD场景，退出")
        simulation_app.close()
        return

    # 设置物理对象（自动选择最合适的对象）
    if not rov_sim.setup_physics_object():
        carb.log_error("❌ 无法设置物理对象，退出")
        simulation_app.close()
        return

    print("🎉 物理仿真启动成功，开始仿真循环...")

    # 仿真循环
    try:
        while simulation_app.is_running():
            rov_sim.step()
    except KeyboardInterrupt:
        print("用户中断仿真")
    except Exception as e:
        carb.log_error(f"仿真错误: {e}")
    finally:
        simulation_app.close()


if __name__ == "__main__":
    main()
