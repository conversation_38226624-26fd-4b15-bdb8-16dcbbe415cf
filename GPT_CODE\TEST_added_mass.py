import numpy as np

def get_loss_coefficients(H_bar):
    """
    获取损失系数。
    注意：此函数使用简化的线性模型。文档中提到使用三次多项式拟合图2的曲线，
    但具体系数未提供，因此此函数仅用于演示逻辑。
    """
    if H_bar <= 0:
        return {
            'kxx': 0.0, 'kyy': 0.0, 'kzz': 0.0,
            'kpp': 0.0, 'kqq': 0.0, 'krr': 0.0,
            'kxq': 0.0, 'kyp': 0.0
        }
    elif H_bar >= 1:
        return {
            'kxx': 1.0, 'kyy': 1.0, 'kzz': 1.0,
            'kpp': 1.0, 'kqq': 1.0, 'krr': 1.0,
            'kxq': 1.0, 'kyp': 1.0
        }
    else:
        # 简化模型：使用线性插值
        return {
            'kxx': H_bar, 'kyy': H_bar, 'kzz': H_bar,
            'kpp': H_bar, 'kqq': H_bar, 'krr': H_bar,
            'kxq': H_bar, 'kyp': H_bar
        }

def calculate_added_mass_effects_updated(
    H_HAUV, z_HAUV,
    v_dot, omega, v, omega_dot
):
    """
    根据表3的精确数值，计算HAUV的附加质量力 (F_add) 和附加质量力矩 (M_add)。
    """
    # [cite_start]--- HAUV的附加质量/惯量参数 (从表3获取) [cite: 1] ---
    # [cite_start]附加质量 [cite: 1]
    X_dot_u = -1.39
    Y_dot_v = -1.39
    Z_dot_w = -0.94
    # [cite_start]附加惯量 [cite: 1]
    K_dot_p = -0.027
    M_dot_q = -0.027
    N_dot_r = -0.013
    # [cite_start]耦合附加质量 [cite: 1]
    X_dot_q = -0.014
    Y_dot_p = -0.014

    # --- 计算浸没比例H_bar ---
    H_bar = (0.5 * H_HAUV - z_HAUV) / H_HAUV

    # --- 获取损失系数k (使用简化的模型) ---
    k = get_loss_coefficients(H_bar)

    # --- 组合公式计算 ---

    # 1. 构造附加质量力矩阵
    Ma_prime = np.diag([k['kxx'] * X_dot_u, k['kyy'] * Y_dot_v, k['kzz'] * Z_dot_w])
    M_coupled = np.array([
        [0, 0, k['kyp'] * Y_dot_p],
        [0, 0, -k['kxq'] * X_dot_q],
        [-k['kyp'] * Y_dot_p, k['kxq'] * X_dot_q, 0]
    ])
    
    F_add = - (Ma_prime @ v_dot + M_coupled @ omega)

    # 2. 构造附加质量力矩矩阵
    # 非耦合附加惯量矩阵 (乘以损失系数)
    Ja_prime = np.diag([k['kpp'] * K_dot_p, k['kqq'] * M_dot_q, k['krr'] * N_dot_r])
    # 耦合附加惯量矩阵 (转置后乘以损失系数)
    J_coupled = M_coupled.T
    ## 耦合附加惯量矩阵 
    # Ja_prime @ omega_dot: 这一部分就是将缩放后的附加惯量矩阵 Ja_prime 乘以角加速度向量 omega_dot
    # J_coupled @ v: 这一部分是将耦合附加惯量矩阵 J_coupled 乘以线性速度向量 v。
    M_add = - (Ja_prime @ omega_dot + J_coupled @ v)

    return F_add, M_add

# --- 示例用法 ---
# [cite_start]假设HAUV的高度为0.3m，垂直位置为-0.1m [cite: 1]
H_HAUV = 0.3
z_HAUV = -0.1

# 假设HAUV的当前运动状态
v_dot_val = np.array([0.1, -0.2, 0.5])
omega_val = np.array([0.05, 0.1, 0.0])
v_val = np.array([0.2, 0.1, 0.3])
omega_dot_val = np.array([0.01, -0.02, 0.03])

# 调用更新后的函数进行计算
F_added_mass, M_added_mass = calculate_added_mass_effects_updated(
    H_HAUV, z_HAUV,
    v_dot_val, omega_val, v_val, omega_dot_val
)

print(f"HAUV高度 H: {H_HAUV} m")
print(f"HAUV垂直位置 z: {z_HAUV} m")
print(f"计算出的浸没比例 H_bar: {(0.5 * H_HAUV - z_HAUV) / H_HAUV:.4f}")
print("---")
print("附加质量力 F_add (N):")
print(F_added_mass)
print("---")
print("附加质量力矩 M_add (N·m):")
print(M_added_mass)