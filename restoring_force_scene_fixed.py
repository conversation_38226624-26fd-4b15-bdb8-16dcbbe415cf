from manim import *
import numpy as np

class RestoringForceSceneOptimized(ThreeDScene):
    def construct(self):
        # --- 1. 场景和相机设置 ---
        self.set_camera_orientation(phi=75 * DEGREES, theta=30 * DEGREES, distance=6)
        self.camera.background_color = "#E2E2E2"

        # --- 2. 地球坐标系 (固定参考系) ---
        earth_axes = ThreeDAxes(
            x_range=[-4, 4, 1], y_range=[-4, 4, 1], z_range=[-4, 4, 1],
            x_length=8, y_length=8, z_length=8,
            axis_config={"color": GRAY}
        )
        earth_labels = earth_axes.get_axis_labels(
            x_label=Tex("$X_e$", color=GRAY).scale(0.8),
            y_label=Tex("$Y_e$", color=GRAY).scale(0.8),
            z_label=Tex("$Z_e$", color=GRAY).scale(0.8)
        )
        self.add(earth_axes, earth_labels)

        # --- 3. 净力向量 (在地球坐标系中) ---
        # 使用简单的线条而不是Vector，避免3D投影问题
        f_net_start = np.array([0.0, 0.0, 0.0])
        f_net_end = np.array([0.0, 0.0, -2.5])
        f_net_earth_vector = Line(f_net_start, f_net_end, color=PURPLE, stroke_width=8)
        f_net_label = Tex("Net Vertical Force", color=PURPLE).scale(0.7).shift(DOWN * 3)
        self.add(f_net_earth_vector, f_net_label)

        # --- 4. HAUV机体坐标系 (动态坐标系) ---
        # 使用Line而不是Vector，确保3D兼容性
        body_x_line = Line(ORIGIN, np.array([2.0, 0.0, 0.0]), color=RED, stroke_width=6)
        body_y_line = Line(ORIGIN, np.array([0.0, 2.0, 0.0]), color=GREEN, stroke_width=6)
        body_z_line = Line(ORIGIN, np.array([0.0, 0.0, 2.0]), color=BLUE, stroke_width=6)
        
        body_axes_vectors = VGroup(body_x_line, body_y_line, body_z_line)
        
        # 为机体坐标系的轴添加标签
        body_axes_labels = VGroup(
            Tex("$X_b$", color=RED).scale(0.7).next_to(body_x_line, RIGHT, buff=0.1),
            Tex("$Y_b$", color=GREEN).scale(0.7).next_to(body_y_line, UP, buff=0.1),
            Tex("$Z_b$", color=BLUE).scale(0.7).next_to(body_z_line, OUT, buff=0.1),
        )

        # 将机体坐标系的轴和标签组合成一个整体
        body_frame = VGroup(body_axes_vectors, body_axes_labels)
        self.play(Create(body_frame))
        self.wait(1)

        # --- 5. 力的分解与动态更新 ---
        # 创建用于显示力的分量的虚线
        force_components = VGroup(
            DashedLine(ORIGIN, ORIGIN, color=RED, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=GREEN, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=BLUE, stroke_width=4)
        )
        self.add(force_components)

        # 定义更新函数，修复数组索引问题
        def update_force_components(mob):
            try:
                # mob 是 body_frame VGroup
                vectors = mob[0]  # 获取轴向量组
                
                # 安全地获取向量方向
                def get_line_direction(line_obj):
                    try:
                        start = line_obj.get_start()
                        end = line_obj.get_end()
                        direction = end - start
                        # 确保是3D向量
                        if len(direction) == 2:
                            direction = np.append(direction, 0.0)
                        elif len(direction) == 1:
                            direction = np.array([direction[0], 0.0, 0.0])
                        return direction[:3]
                    except Exception as e:
                        print(f"获取线条方向失败: {e}")
                        return np.array([1.0, 0.0, 0.0])  # 默认方向
                
                # 获取当前机体坐标系的三个轴方向
                body_x_vec = get_line_direction(vectors[0])
                body_y_vec = get_line_direction(vectors[1])
                body_z_vec = get_line_direction(vectors[2])
                
                # 安全归一化
                def safe_normalize(vec):
                    norm = np.linalg.norm(vec)
                    if norm < 1e-10:
                        return np.array([1.0, 0.0, 0.0])
                    return vec / norm
                
                body_x_hat = safe_normalize(body_x_vec)
                body_y_hat = safe_normalize(body_y_vec)
                body_z_hat = safe_normalize(body_z_vec)

                # 构造旋转矩阵
                R_matrix = np.column_stack([body_x_hat, body_y_hat, body_z_hat])
                
                # 地球坐标系下的净力向量（固定值）
                f_net_earth = np.array([0.0, 0.0, -2.5])
                
                # 将净力从地球坐标系转换到机体坐标系
                f_net_body = R_matrix.T.dot(f_net_earth)
                
                # 计算力的分量向量（在地球坐标系中显示）
                comp_x_vec = f_net_body[0] * body_x_hat
                comp_y_vec = f_net_body[1] * body_y_hat
                comp_z_vec = f_net_body[2] * body_z_hat
                
                # 更新虚线显示
                origin = np.array([0.0, 0.0, 0.0])
                force_components[0].put_start_and_end_on(origin, comp_x_vec)
                force_components[1].put_start_and_end_on(origin, comp_y_vec)
                force_components[2].put_start_and_end_on(origin, comp_z_vec)
                
            except Exception as e:
                print(f"更新力分量时出错: {e}")
                import traceback
                traceback.print_exc()
                
                # 设置默认值以避免崩溃
                origin = np.array([0.0, 0.0, 0.0])
                try:
                    force_components[0].put_start_and_end_on(origin, origin)
                    force_components[1].put_start_and_end_on(origin, origin)
                    force_components[2].put_start_and_end_on(origin, origin)
                except:
                    pass  # 如果连这个都失败，就忽略

        # 将更新函数添加到机体坐标系上
        body_frame.add_updater(update_force_components)

        # --- 6. 动画演示 ---
        # 简化文本，避免字体问题
        intro_text = Tex("Force Decomposition", color=BLACK).scale(0.8).to_corner(UL)
        self.add_fixed_in_frame_mobjects(intro_text)
        self.play(FadeIn(intro_text))
        
        # 俯仰动画 (绕机体Y轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=45 * DEGREES,
                axis=np.array([0, 1, 0]),  # 使用固定的Y轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(1)

        # 滚转动画 (绕机体X轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=30 * DEGREES,
                axis=np.array([1, 0, 0]),  # 使用固定的X轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(2)
        
        # --- 7. 清理场景 ---
        self.play(FadeOut(intro_text))
        body_frame.remove_updater(update_force_components)
        self.play(FadeOut(body_frame, force_components, f_net_earth_vector, f_net_label))
        self.wait(1)
