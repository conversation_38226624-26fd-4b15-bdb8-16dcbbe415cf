from manim import *
import numpy as np

class RestoringForceSceneOptimized(ThreeDScene):
    def construct(self):
        # --- 1. 场景和相机设置 ---
        # 设置3D场景的相机视角和背景颜色
        self.set_camera_orientation(phi=75 * DEGREES, theta=30 * DEGREES, distance=6)
        self.camera.background_color = "#E2E2E2"

        # --- 2. 地球坐标系 (固定参考系) ---
        # 创建一个固定的3D坐标系作为参考
        earth_axes = ThreeDAxes(
            x_range=[-4, 4, 1], y_range=[-4, 4, 1], z_range=[-4, 4, 1],
            x_length=8, y_length=8, z_length=8,
            axis_config={"color": GRAY}
        )
        # 为地球坐标系添加标签
        earth_labels = earth_axes.get_axis_labels(
            x_label=Tex("$X_e$", color=GRAY).scale(0.8),
            y_label=Tex("$Y_e$", color=GRAY).scale(0.8),
            z_label=Tex("$Z_e$", color=GRAY).scale(0.8)
        )
        self.add(earth_axes, earth_labels)

        # --- 3. 净力向量 (在地球坐标系中) ---
        # 创建一个始终垂直向下的向量，代表净力（如重力）
        # 这个向量在地球坐标系中是固定的
        f_net_earth_vector = Vector(direction=[0, 0, -2.5], color=PURPLE, stroke_width=8)
        f_net_label = Tex("Net Vertical Force", color=PURPLE).scale(0.7).next_to(f_net_earth_vector, DOWN, buff=0.2)
        # 在3D场景中，标签需要始终面向相机
        f_net_label.add_updater(lambda m: m.next_to(f_net_earth_vector, DOWN, buff=0.2))
        self.add(f_net_earth_vector, f_net_label)

        # --- 4. HAUV机体坐标系 (动态坐标系) ---
        # 创建代表机体坐标系的三个正交向量
        body_axes_vectors = VGroup(
            Vector(RIGHT * 2, color=RED, stroke_width=6),    # X_b 轴
            Vector(UP * 2, color=GREEN, stroke_width=6),   # Y_b 轴
            Vector(OUT * 2, color=BLUE, stroke_width=6),     # Z_b 轴
        )
        # 为机体坐标系的轴添加标签
        body_axes_labels = VGroup(
            Tex("$X_b$", color=RED).scale(0.7).next_to(body_axes_vectors[0], body_axes_vectors[0].get_vector(), buff=0.1),
            Tex("$Y_b$", color=GREEN).scale(0.7).next_to(body_axes_vectors[1], body_axes_vectors[1].get_vector(), buff=0.1),
            Tex("$Z_b$", color=BLUE).scale(0.7).next_to(body_axes_vectors[2], body_axes_vectors[2].get_vector(), buff=0.1),
        )

        # 将机体坐标系的轴和标签组合成一个整体，方便统一操作
        body_frame = VGroup(body_axes_vectors, body_axes_labels)

        self.play(Create(body_frame))
        self.wait(1)

        # --- 5. 力的分解与动态更新 ---
        # 创建用于显示力的分量的虚线
        force_components = VGroup(
            DashedLine(ORIGIN, ORIGIN, color=RED, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=GREEN, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=BLUE, stroke_width=4)
        )
        self.add(force_components)

        # 定义更新函数，用于在机体旋转时，实时计算并显示力的分量
        def update_force_components(mob):
            # mob 是 body_frame VGroup
            # 从 VGroup 中获取轴向量组
            vectors = mob[0]
            
            # 构造旋转矩阵
            # VGroup 没有 .get_matrix() 方法。我们需要根据当前轴向量的方向手动构造旋转矩阵。
            # 获取当前机体坐标系的三个轴在地球坐标系下的方向向量
            body_x_hat = normalize(vectors[0].get_vector())
            body_y_hat = normalize(vectors[1].get_vector())
            body_z_hat = normalize(vectors[2].get_vector())

            # 构造从机体坐标系到地球坐标系的旋转矩阵 R
            # R 的列向量是机体坐标系的基向量在地球坐标系下的表示
            R_matrix = np.column_stack([body_x_hat, body_y_hat, body_z_hat])
            
            # 地球坐标系下的净力向量 (这是一个固定值)
            f_net_in_earth = f_net_earth_vector.get_vector()
            
            # 将净力向量从地球坐标系转换到机体坐标系
            # F_body = R_transpose * F_earth
            f_net_in_body = R_matrix.T @ f_net_in_earth
            
            # 机体坐标系的三个轴向量在地球坐标系中的表示 (未归一化)
            body_x_axis_in_earth = vectors[0].get_vector()
            body_y_axis_in_earth = vectors[1].get_vector()
            body_z_axis_in_earth = vectors[2].get_vector()

            # 计算每个分量向量。分量向量 = (在机体坐标系下的力的标量) * (机体轴在地球坐标系下的方向向量)
            # 这样得到的向量是在地球坐标系中描述的，可以直接在场景中显示
            comp_x_vec = f_net_in_body[0] * normalize(body_x_axis_in_earth)
            comp_y_vec = f_net_in_body[1] * normalize(body_y_axis_in_earth)
            comp_z_vec = f_net_in_body[2] * normalize(body_z_axis_in_earth)

            # 更新虚线的位置来显示这些分量
            force_components[0].put_start_and_end_on(ORIGIN, comp_x_vec)
            force_components[1].put_start_and_end_on(ORIGIN, comp_y_vec)
            force_components[2].put_start_and_end_on(ORIGIN, comp_z_vec)

        # 将更新函数添加到机体坐标系上
        body_frame.add_updater(update_force_components)

        # --- 6. 动画演示 ---
        # 将 Tex 更改为 Text 并指定中文字体，以避免LaTeX编码错误
        intro_text = Text("当HAUV倾斜时，净力被分解...", font="SimHei", font_size=40).to_corner(UL).set_color(BLACK)
        self.add_fixed_in_frame_mobjects(intro_text) # 使文本固定在屏幕上
        
        # 【修复】将 Write 替换为 FadeIn，以避免在3D场景中渲染固定2D对象时出错
        self.play(FadeIn(intro_text, shift=UP))
        
        # 俯仰动画 (绕机体Y轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=45 * DEGREES,
                axis=body_frame[0][1].get_vector(), # 围绕 Y_b 轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(1)

        # 滚转动画 (绕机体X轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=30 * DEGREES,
                axis=body_frame[0][0].get_vector(), # 围绕 X_b 轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(2)
        
        # --- 7. 清理场景 ---
        self.play(FadeOut(intro_text))
        
        # 移除更新器，停止计算
        body_frame.remove_updater(update_force_components)
        
        # 淡出所有相关物体
        self.play(FadeOut(body_frame, force_components, f_net_earth_vector, f_net_label))
        self.wait(1)
