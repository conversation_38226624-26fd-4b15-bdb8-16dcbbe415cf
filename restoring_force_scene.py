from manim import *
import numpy as np

# 定义归一化函数，以防manim中没有
def normalize_vector(vec):
    """归一化向量"""
    vec_array = np.array(vec).flatten()
    norm = np.linalg.norm(vec_array)
    if norm == 0:
        return vec_array
    return vec_array / norm

# 确保ORIGIN被定义（以防导入问题）
try:
    ORIGIN
except NameError:
    ORIGIN = np.array([0.0, 0.0, 0.0])

class RestoringForceSceneOptimized(ThreeDScene):
    def construct(self):
        # --- 1. 场景和相机设置 ---
        # 设置3D场景的相机视角和背景颜色
        self.set_camera_orientation(phi=75 * DEGREES, theta=30 * DEGREES, distance=6)
        self.camera.background_color = "#E2E2E2"

        # --- 2. 地球坐标系 (固定参考系) ---
        # 创建一个固定的3D坐标系作为参考
        earth_axes = ThreeDAxes(
            x_range=[-4, 4, 1], y_range=[-4, 4, 1], z_range=[-4, 4, 1],
            x_length=8, y_length=8, z_length=8,
            axis_config={"color": GRAY}
        )
        # 为地球坐标系添加标签
        earth_labels = earth_axes.get_axis_labels(
            x_label=Tex("$X_e$", color=GRAY).scale(0.8),
            y_label=Tex("$Y_e$", color=GRAY).scale(0.8),
            z_label=Tex("$Z_e$", color=GRAY).scale(0.8)
        )
        self.add(earth_axes, earth_labels)

        # --- 3. 净力向量 (在地球坐标系中) ---
        # 创建一个始终垂直向下的向量，代表净力（如重力）
        # 这个向量在地球坐标系中是固定的
        f_net_earth_vector = Vector(direction=[0, 0, -2.5], color=PURPLE, stroke_width=8)
        f_net_label = Tex("Net Vertical Force", color=PURPLE).scale(0.7).next_to(f_net_earth_vector, DOWN, buff=0.2)
        # 在3D场景中，标签需要始终面向相机
        f_net_label.add_updater(lambda m: m.next_to(f_net_earth_vector, DOWN, buff=0.2))
        self.add(f_net_earth_vector, f_net_label)

        # --- 4. HAUV机体坐标系 (动态坐标系) ---
        # 创建代表机体坐标系的三个正交向量
        body_axes_vectors = VGroup(
            Vector(RIGHT * 2, color=RED, stroke_width=6),    # X_b 轴
            Vector(UP * 2, color=GREEN, stroke_width=6),   # Y_b 轴
            Vector(OUT * 2, color=BLUE, stroke_width=6),     # Z_b 轴
        )
        # 为机体坐标系的轴添加标签
        body_axes_labels = VGroup(
            Tex("$X_b$", color=RED).scale(0.7).next_to(body_axes_vectors[0], body_axes_vectors[0].get_vector(), buff=0.1),
            Tex("$Y_b$", color=GREEN).scale(0.7).next_to(body_axes_vectors[1], body_axes_vectors[1].get_vector(), buff=0.1),
            Tex("$Z_b$", color=BLUE).scale(0.7).next_to(body_axes_vectors[2], body_axes_vectors[2].get_vector(), buff=0.1),
        )

        # 将机体坐标系的轴和标签组合成一个整体，方便统一操作
        body_frame = VGroup(body_axes_vectors, body_axes_labels)

        self.play(Create(body_frame))
        self.wait(1)

        # --- 5. 力的分解与动态更新 ---
        # 创建用于显示力的分量的虚线
        force_components = VGroup(
            DashedLine(ORIGIN, ORIGIN, color=RED, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=GREEN, stroke_width=4),
            DashedLine(ORIGIN, ORIGIN, color=BLUE, stroke_width=4)
        )
        self.add(force_components)

        # 定义更新函数，用于在机体旋转时，实时计算并显示力的分量
        def update_force_components(mob):
            try:
                # mob 是 body_frame VGroup
                # 从 VGroup 中获取轴向量组
                vectors = mob[0]

                # 获取当前机体坐标系的三个轴向量
                body_x_vec = vectors[0].get_vector()
                body_y_vec = vectors[1].get_vector()
                body_z_vec = vectors[2].get_vector()

                # 转换为numpy数组并确保是3D向量
                def ensure_3d_vector(vec):
                    vec_array = np.array(vec, dtype=float).flatten()
                    if len(vec_array) == 2:
                        vec_array = np.append(vec_array, 0.0)
                    elif len(vec_array) == 1:
                        vec_array = np.array([vec_array[0], 0.0, 0.0])
                    elif len(vec_array) == 0:
                        vec_array = np.array([0.0, 0.0, 0.0])
                    return vec_array[:3]  # 只取前3个分量

                body_x_hat = ensure_3d_vector(body_x_vec)
                body_y_hat = ensure_3d_vector(body_y_vec)
                body_z_hat = ensure_3d_vector(body_z_vec)

                # 归一化向量
                def safe_normalize(vec):
                    norm = np.linalg.norm(vec)
                    if norm < 1e-10:  # 避免除零
                        return np.array([1.0, 0.0, 0.0])  # 默认方向
                    return vec / norm

                body_x_hat = safe_normalize(body_x_hat)
                body_y_hat = safe_normalize(body_y_hat)
                body_z_hat = safe_normalize(body_z_hat)

                # 构造旋转矩阵
                R_matrix = np.column_stack([body_x_hat, body_y_hat, body_z_hat])

                # 获取地球坐标系下的净力向量
                f_net_earth_raw = f_net_earth_vector.get_vector()
                f_net_earth = ensure_3d_vector(f_net_earth_raw)

                # 将净力从地球坐标系转换到机体坐标系
                f_net_body = R_matrix.T.dot(f_net_earth)

                # 计算力的分量向量（在地球坐标系中显示）
                comp_x_vec = f_net_body[0] * body_x_hat
                comp_y_vec = f_net_body[1] * body_y_hat
                comp_z_vec = f_net_body[2] * body_z_hat

                # 更新虚线显示
                origin = np.array([0.0, 0.0, 0.0])
                force_components[0].put_start_and_end_on(origin, comp_x_vec)
                force_components[1].put_start_and_end_on(origin, comp_y_vec)
                force_components[2].put_start_and_end_on(origin, comp_z_vec)

            except Exception as e:
                # 错误处理：打印错误信息并设置默认值
                print(f"更新力分量时出错: {e}")
                import traceback
                traceback.print_exc()

                # 设置默认值以避免崩溃
                origin = np.array([0.0, 0.0, 0.0])
                force_components[0].put_start_and_end_on(origin, origin)
                force_components[1].put_start_and_end_on(origin, origin)
                force_components[2].put_start_and_end_on(origin, origin)

        # 将更新函数添加到机体坐标系上
        body_frame.add_updater(update_force_components)

        # --- 6. 动画演示 ---
        # 将 Tex 更改为 Text 并指定中文字体，以避免LaTeX编码错误
        intro_text = Text("当HAUV倾斜时，净力被分解...", font="SimHei", font_size=40).to_corner(UL).set_color(BLACK)
        self.add_fixed_in_frame_mobjects(intro_text) # 使文本固定在屏幕上
        
        # 【修复】将 Write 替换为 FadeIn，以避免在3D场景中渲染固定2D对象时出错
        self.play(FadeIn(intro_text, shift=UP))
        
        # 俯仰动画 (绕机体Y轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=45 * DEGREES,
                axis=body_frame[0][1].get_vector(), # 围绕 Y_b 轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(1)

        # 滚转动画 (绕机体X轴旋转)
        self.play(
            Rotate(
                body_frame,
                angle=30 * DEGREES,
                axis=body_frame[0][0].get_vector(), # 围绕 X_b 轴
                rate_func=smooth,
                run_time=3
            )
        )
        self.wait(2)
        
        # --- 7. 清理场景 ---
        self.play(FadeOut(intro_text))
        
        # 移除更新器，停止计算
        body_frame.remove_updater(update_force_components)
        
        # 淡出所有相关物体
        self.play(FadeOut(body_frame, force_components, f_net_earth_vector, f_net_label))
        self.wait(1)
