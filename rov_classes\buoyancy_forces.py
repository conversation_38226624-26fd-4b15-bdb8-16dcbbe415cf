"""
浮力计算模块 - 带旋转的浮力计算
简化版本：移除BaseNode架构，改为直接函数调用
"""

import math
import numpy as np


def calculate_buoyancy_forces(volume, height, z_position, rotation, 
                            water_density=1.0, gravity=9.8, debug=False):
    """
    计算浮力并根据物体方向进行旋转的函数
    
    Args:
        volume: 物体体积 (m³)
        height: 物体高度 (m)
        z_position: 物体Z位置 (m)
        rotation: 旋转角度 [roll, pitch, yaw] (度)
        water_density: 水密度 (kg/m³)
        gravity: 重力加速度 (m/s²)
        debug: 是否输出调试信息
    
    Returns:
        dict: {
            'x_force': X方向的浮力 (N),
            'y_force': Y方向的浮力 (N),
            'z_force': Z方向的浮力 (N)
        }
    """
    # 计算浸没体积
    submerged_height = _calculate_submerged_height(z_position, height)
    submerged_volume = volume * submerged_height
    
    # 计算浮力大小
    buoyancy_force = water_density * submerged_volume * gravity
    
    # 浮力总是向上的！不应该随物体旋转而改变方向
    # 浮力是环境施加给物体的力，方向固定在世界坐标系的+Z方向
    
    result = {
        'x_force': 0.0,  # 浮力不产生X方向的力
        'y_force': 0.0,  # 浮力不产生Y方向的力  
        'z_force': float(buoyancy_force)  # 浮力总是向上(+Z)
    }
    
    # 调试输出
    if debug and buoyancy_force > 0.1:
        print(f"🔧 浮力计算详情:")
        print(f"   浸没高度: {submerged_height:.3f}")
        print(f"   浸没体积: {submerged_volume:.3f}m³")
        print(f"   浮力大小: {buoyancy_force:.2f}N")
        print(f"   旋转角度: {rotation} 度")
        print(f"   ✅ 浮力修正: 总是向上，不受物体旋转影响")
    
    return result


def _calculate_submerged_height(z_position, height):
    """计算物体的浸没高度比例"""
    center_of_h = height / 2
    if z_position >= center_of_h:
        return 0.0
    elif z_position < -center_of_h:
        return 1.0
    else:
        return (center_of_h - z_position) / height
