import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
import numpy as np

def plot_restoring_force_components(roll_deg, pitch_deg):
    """
    绘制HAUV在指定姿态下，重力和浮力合力在机体坐标系下的分量示意图。
    
    Args:
        roll_deg (float): 翻滚角，单位为度。
        pitch_deg (float): 俯仰角，单位为度。
    """
    
    # 将角度转换为弧度
    roll = np.deg2rad(roll_deg)
    pitch = np.deg2rad(pitch_deg)
    
    # 假设一个净力（重力-浮力），在地球坐标系下是纯粹的垂直力，方向向下。
    # 为了简化，我们设其大小为1，方向为[0, 0, -1]。
    f_net_earth = np.array([0, 0, -1])

    # 构造从机体坐标系到地球坐标系的旋转矩阵R1
    # 这与文档中公式(1)的R1矩阵一致
    c_r, s_r = np.cos(roll), np.sin(roll)
    c_p, s_p = np.cos(pitch), np.sin(pitch)
    
    R1 = np.array([
        [c_p, 0, s_p],
        [s_r*s_p, c_r, -s_r*c_p],
        [-c_r*s_p, s_r, c_r*c_p]
    ])
    
    # 得到机体坐标系的x, y, z轴在地球坐标系下的方向
    # 旋转矩阵R1的列向量就是机体轴
    body_x_axis = R1[:, 0]
    body_y_axis = R1[:, 1]
    body_z_axis = R1[:, 2]
    
    # 将净力向量从地球坐标系转换到HAUV的机体坐标系下
    # 这就是公式(7)的核心：g1 = -R1.T @ f_net_earth
    g1 = R1.T @ f_net_earth
    
    # --- 绘图部分 ---
    fig = plt.figure(figsize=(10, 8))
    ax = fig.add_subplot(111, projection='3d')
    ax.set_title(f'HAUV在姿态 (Roll={roll_deg}°, Pitch={pitch_deg}°) 下的恢复力分解')
    
    # 绘制地球坐标系轴
    ax.quiver(0, 0, 0, 1, 0, 0, color='gray', label='Earth X', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, 0, 1, 0, color='gray', label='Earth Y', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, 0, 0, 1, color='gray', label='Earth Z', arrow_length_ratio=0.1)
    
    # 绘制HAUV的机体坐标系轴
    ax.quiver(0, 0, 0, body_x_axis[0], body_x_axis[1], body_x_axis[2], color='r', label='Body X (HAUV Forward)', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, body_y_axis[0], body_y_axis[1], body_y_axis[2], color='g', label='Body Y (HAUV Right)', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, body_z_axis[0], body_z_axis[1], body_z_axis[2], color='b', label='Body Z (HAUV Down)', arrow_length_ratio=0.1)
    
    # 绘制地球坐标系下的净力向量
    ax.quiver(0, 0, 0, f_net_earth[0], f_net_earth[1], f_net_earth[2], color='purple', label='Net Vertical Force (Earth Frame)', arrow_length_ratio=0.1, linewidth=3)
    
    # 绘制净力向量在HAUV机体坐标系下的分量
    ax.quiver(0, 0, 0, g1[0], 0, 0, color='r', linestyle='--', label='Force on Body X', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, 0, g1[1], 0, color='g', linestyle='--', label='Force on Body Y', arrow_length_ratio=0.1)
    ax.quiver(0, 0, 0, 0, 0, g1[2], color='b', linestyle='--', label='Force on Body Z', arrow_length_ratio=0.1)
    
    # 设置图表属性
    ax.set_xlim([-1, 1])
    ax.set_ylim([-1, 1])
    ax.set_zlim([-1, 1])
    ax.set_xlabel('X-axis')
    ax.set_ylabel('Y-axis')
    ax.set_zlabel('Z-axis')
    ax.legend(loc='upper left', fontsize='small')
    ax.grid(True)
    plt.show()

# --- 示例调用 ---
# 场景一: HAUV 完全水平
plot_restoring_force_components(roll_deg=0, pitch_deg=0)

# 场景二: HAUV 俯仰30度 (抬头)
plot_restoring_force_components(roll_deg=0, pitch_deg=30)

# 场景三: HAUV 翻滚45度 (向右侧倾斜)
plot_restoring_force_components(roll_deg=45, pitch_deg=0)